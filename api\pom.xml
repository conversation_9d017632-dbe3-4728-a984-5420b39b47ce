<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>org.jeecgframework.boot</groupId>
  <artifactId>jeecg-boot-parent</artifactId>
  <version>2.7.0</version>
  <packaging>pom</packaging>
  
  	<parent>
	    <groupId>org.springframework.boot</groupId>
	    <artifactId>spring-boot-starter-parent</artifactId>
	    <version>2.1.3.RELEASE</version>
	    <relativePath/>
	</parent>


	<modules>
		<module>jeecg-boot-base-common</module>
		<module>jeecg-boot-module-system</module>
    </modules>

	<distributionManagement>
	  	<repository>
            <id>jeecg</id>
            <name>jeecg Repository</name>
            <url>https://maven.jeecg.org/nexus/content/repositories/jeecg</url>
        </repository>
        <snapshotRepository>
	        <id>jeecg-snapshots</id>
            <name>jeecg Snapshot Repository</name>
            <url>https://oss.sonatype.org/content/repositories/snapshots</url>
	    </snapshotRepository>
	</distributionManagement>
	
	<repositories>
		<repository>
            <id>aliyun</id>
            <name>aliyun Repository</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <snapshots>
				<enabled>false</enabled>
			</snapshots>
        </repository>
		<repository>
            <id>jeecg</id>
            <name>jeecg Repository</name>
            <url>https://maven.jeecg.org/nexus/content/repositories/jeecg</url>
            <snapshots>
				<enabled>false</enabled>
			</snapshots>
        </repository>
	</repositories>
	
    <properties>
        <jeecgboot.version>2.2.0</jeecgboot.version>
        <java.version>1.8</java.version>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <mybatis-plus.version>3.1.2</mybatis-plus.version>
        <druid.version>1.1.17</druid.version>
        <jwt.version>0.9.1</jwt.version>
        <commons.version>2.6</commons.version>
        <aliyun-java-sdk-core.version>3.2.3</aliyun-java-sdk-core.version>
		<aliyun-java-sdk-dysmsapi.version>1.0.0</aliyun-java-sdk-dysmsapi.version>
		<aliyun.oss.version>3.6.0</aliyun.oss.version>
		<guava.version>30.0-jre</guava.version>
		<log4j2.version>2.17.0</log4j2.version>
		<logback.version>1.2.9</logback.version>
    </properties>
    
	<dependencies>
		<!--集成springmvc框架并实现自动配置 -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
		<dependency>
    		<groupId>org.springframework.boot</groupId>
    		<artifactId>spring-boot-starter-mail</artifactId>
		</dependency>
		<dependency>
	        <groupId>org.springframework.boot</groupId>
	        <artifactId>spring-boot-starter-test</artifactId>
	        <scope>test</scope>
	    </dependency>
	    <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
		
		<!-- commons -->
		<dependency>
			<groupId>commons-io</groupId>
			<artifactId>commons-io</artifactId>
			<version>${commons.version}</version>
		</dependency>
		<dependency>
			<groupId>commons-lang</groupId>
			<artifactId>commons-lang</artifactId>
			<version>${commons.version}</version>
		</dependency>

		<!-- freemarker -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-freemarker</artifactId>
		</dependency>
		
		<!-- Lombok -->
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
		</dependency>
		
		<!-- mybatis-plus -->
		 <dependency>
	        <groupId>com.baomidou</groupId>
	        <artifactId>mybatis-plus-boot-starter</artifactId>
	        <version>${mybatis-plus.version}</version>
	    </dependency>
		
		<!-- druid -->
       <dependency>
		   <groupId>com.alibaba</groupId>
		   <artifactId>druid-spring-boot-starter</artifactId>
		   <version>${druid.version}</version>
		</dependency>
		
		<!-- 动态数据源 -->
		<dependency>
		  <groupId>com.baomidou</groupId>
		  <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
		  <version>2.5.4</version>
		</dependency>
				
		<!-- json -->
		<dependency>
         	<groupId>com.alibaba</groupId>
         	<artifactId>fastjson</artifactId>
         	<version>1.2.83</version>
        </dependency>
      
		<!--mysql-->
		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
			<version>8.0.28</version>
			<scope>runtime</scope>
		</dependency>
		<!--  sqlserver-->
		<dependency>
		    <groupId>com.microsoft.sqlserver</groupId>
		    <artifactId>sqljdbc4</artifactId>
		    <version>4.0</version>
		    <scope>runtime</scope>
		</dependency>
		<!-- oracle驱动 -->
		<dependency>
		    <groupId>com.oracle</groupId>
		    <artifactId>ojdbc6</artifactId>
		    <version>11.2.0.3</version>
			<scope>runtime</scope>
		</dependency>
        <!-- postgresql驱动 -->
		<dependency>
			<groupId>org.postgresql</groupId>
			<artifactId>postgresql</artifactId>
			<version>42.2.27</version>
			<scope>runtime</scope>
		</dependency>
		<dependency>
			<groupId>org.hibernate</groupId>
			<artifactId>hibernate-re</artifactId>
			<version>2.1.5</version>
		</dependency>
		<dependency>
			<groupId>org.hibernate</groupId>
			<artifactId>hibernate-core</artifactId>
			<version>5.3.20.Final</version>
			<exclusions>
				<exclusion>
					<groupId>commons-collections</groupId>
					<artifactId>commons-collections</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		
		 <!-- Quartz定时任务 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-quartz</artifactId>
        </dependency>
        
		<!--JWT-->
		<dependency>
			<groupId>com.auth0</groupId>
			<artifactId>java-jwt</artifactId>
			<version>3.7.0</version>
		</dependency>
		
		<!--shiro-->
		<dependency>
		    <groupId>org.apache.shiro</groupId>
		    <artifactId>shiro-spring-boot-starter</artifactId>
		    <version>1.7.0</version>
		</dependency>
		<!-- shiro-redis -->
        <dependency>
            <groupId>org.crazycake</groupId>
            <artifactId>shiro-redis</artifactId>
            <version>3.1.0</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.shiro</groupId>
                    <artifactId>shiro-core</artifactId>
                </exclusion>
				<exclusion>
					<artifactId>guava</artifactId>
					<groupId>com.google.guava</groupId>
				</exclusion>
			</exclusions>
        </dependency>

		<!-- Swagger API文档 -->
        <dependency>
		    <groupId>io.springfox</groupId>
		    <artifactId>springfox-swagger2</artifactId>
		    <version>2.9.2</version>
			<exclusions>
				<exclusion>
					<artifactId>swagger-annotations</artifactId>
					<groupId>io.swagger</groupId>
				</exclusion>
				<exclusion>
					<artifactId>swagger-models</artifactId>
					<groupId>io.swagger</groupId>
				</exclusion>
				<exclusion>
					<artifactId>guava</artifactId>
					<groupId>com.google.guava</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
		    <groupId>io.springfox</groupId>
		    <artifactId>springfox-swagger-ui</artifactId>
		    <version>2.9.2</version>
		</dependency>
		<dependency>
			<groupId>com.github.xiaoymin</groupId>
			<artifactId>swagger-bootstrap-ui</artifactId>
			<version>1.9.3</version>
		</dependency>
		<dependency>
			<groupId>io.springfox</groupId>
			<artifactId>springfox-bean-validators</artifactId>
			<version>2.9.2</version>
			<exclusions>
				<exclusion>
					<artifactId>guava</artifactId>
					<groupId>com.google.guava</groupId>
				</exclusion>
			</exclusions>
		</dependency>
        <!-- # 增加两个配置解决 NumberFormatException -->
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-annotations</artifactId>
            <version>1.5.22</version>
        </dependency>
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-models</artifactId>
            <version>1.5.22</version>
        </dependency>
		
		
		<!-- Redis -->
		<dependency>
			 <groupId>org.springframework.boot</groupId>
			  <artifactId>spring-boot-starter-data-redis</artifactId>
		</dependency>
		<dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
        </dependency>
        
		<!-- 代码生成器 -->
		<!-- 如果下载失败，看这个链接http://jeecg-boot.mydoc.io/?t=345672 -->
		<dependency>
			<groupId>org.jeecgframework.boot</groupId>
			<artifactId>codegenerate</artifactId>
			<version>1.2.0</version>
		</dependency>
		
        <!-- AutoPoi Excel工具类-->
		<dependency>
			<groupId>org.jeecgframework</groupId>
			<artifactId>autopoi-web</artifactId>
			<version>1.1.1</version>
			<exclusions>
				<exclusion>
					<groupId>commons-codec</groupId>
					<artifactId>commons-codec</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		
		<dependency>
		    <groupId>cn.hutool</groupId>
		    <artifactId>hutool-all</artifactId>
		    <version>4.5.11</version>
		</dependency>

		<!-- mini文件存储服务 -->
		<dependency>
			<groupId>io.minio</groupId>
			<artifactId>minio</artifactId>
			<version>4.0.0</version>
			<exclusions>
				<exclusion>
					<artifactId>guava</artifactId>
					<groupId>com.google.guava</groupId>
				</exclusion>
				<exclusion>
					<artifactId>jsr305</artifactId>
					<groupId>com.google.code.findbugs</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava</artifactId>
			<version>${guava.version}</version>
		</dependency>

		<!-- 阿里云短信 -->
		<dependency>
			<groupId>com.aliyun</groupId>
			<artifactId>aliyun-java-sdk-dysmsapi</artifactId>
			<version>${aliyun-java-sdk-dysmsapi.version}</version>
		</dependency>
		
		<!-- websocket -->
		<dependency>
		   <groupId>org.springframework.boot</groupId>
		   <artifactId>spring-boot-starter-websocket</artifactId>
		</dependency>

		<!-- aliyun oss -->
		<dependency>
			<groupId>com.aliyun.oss</groupId>
			<artifactId>aliyun-sdk-oss</artifactId>
			<version>${aliyun.oss.version}</version>
		</dependency>

		<!--类似python的requests请求包-->
		<dependency>
			<groupId>net.dongliu</groupId>
			<artifactId>requests</artifactId>
			<version>4.18.1</version>
		</dependency>

		<!-- 七牛云SDK -->
		<dependency>
			<groupId>com.qiniu</groupId>
			<artifactId>qiniu-java-sdk</artifactId>
			<version>7.2.23</version>
		</dependency>

		<!--微信api-->
		<dependency>
			<groupId>com.github.liyiorg</groupId>
			<artifactId>weixin-popular</artifactId>
			<version>2.8.30</version>
		</dependency>
		<!-- 第三方登录 -->
		<dependency>
			<groupId>com.xkcoding.justauth</groupId>
			<artifactId>justauth-spring-boot-starter</artifactId>
			<version>1.3.2</version>
			<exclusions>
				<exclusion>
					<artifactId>fastjson</artifactId>
					<groupId>com.alibaba</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<!-- tianai-captcha验证码 -->
		<dependency>
			<groupId>cloud.tianai.captcha</groupId>
			<artifactId>tianai-captcha-springboot-starter</artifactId>
			<version>1.5.2</version>
		</dependency>

	</dependencies>

	<dependencyManagement>
		<dependencies>
			<!-- jeecg-boot-base-common -->
			<dependency>
	    		<groupId>org.jeecgframework.boot</groupId>
	    		<artifactId>jeecg-boot-base-common</artifactId>
	    		<version>${jeecgboot.version}</version>
	    	</dependency>
	    	<!-- 七牛云SDK -->
			<dependency>
				<groupId>com.qiniu</groupId>
				<artifactId>qiniu-java-sdk</artifactId>
				<version>7.2.23</version>
			</dependency>
		</dependencies>
	</dependencyManagement>
	<build>
		<plugins>
			<!--<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>
			 指定JDK编译版本 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <!-- 打包跳过测试 -->
            <plugin>
		        <groupId>org.apache.maven.plugins</groupId>
		        <artifactId>maven-surefire-plugin</artifactId>
		        <configuration>
		          <skipTests>true</skipTests>
		        </configuration>
	        </plugin>
	         <!-- 避免font文件的二进制文件格式压缩破坏 -->
	         <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <configuration>
                    <nonFilteredFileExtensions>
                        <nonFilteredFileExtension>woff</nonFilteredFileExtension>
                        <nonFilteredFileExtension>woff2</nonFilteredFileExtension>
                        <nonFilteredFileExtension>eot</nonFilteredFileExtension>
                        <nonFilteredFileExtension>ttf</nonFilteredFileExtension>
                        <nonFilteredFileExtension>svg</nonFilteredFileExtension>
                    </nonFilteredFileExtensions>
                </configuration>
            </plugin>
		</plugins>
		<resources>
			<resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
			<resource>
				<directory>src/main/java</directory>
				<includes>
					<include>**/*.xml</include>
					<include>**/*.json</include>
					<include>**/*.ftl</include>
				</includes>
			</resource>
		</resources>
	</build>
</project>