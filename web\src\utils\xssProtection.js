import DOMPurify from 'dompurify'

/**
 * XSS防护工具类
 * 使用DOMPurify进行HTML内容清理，防止XSS攻击
 * 
 * <AUTHOR> System
 * @date 2024-01-01
 */

/**
 * 默认的DOMPurify配置
 */
const DEFAULT_CONFIG = {
  // 允许的标签
  ALLOWED_TAGS: [
    'p', 'br', 'strong', 'em', 'u', 's', 'sub', 'sup',
    'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
    'ul', 'ol', 'li',
    'blockquote', 'pre', 'code',
    'table', 'thead', 'tbody', 'tr', 'th', 'td',
    'a', 'img'
  ],
  
  // 允许的属性
  ALLOWED_ATTR: [
    'href', 'title', 'alt', 'src', 'width', 'height',
    'class', 'id', 'style'
  ],
  
  // 允许的URI协议
  ALLOWED_URI_REGEXP: /^(?:(?:(?:f|ht)tps?|mailto|tel|callto|cid|xmpp|data):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i,
  
  // 保持注释
  KEEP_CONTENT: false,
  
  // 返回DOM节点而不是字符串
  RETURN_DOM: false,
  
  // 返回DOM片段
  RETURN_DOM_FRAGMENT: false,
  
  // 返回可信任的类型
  RETURN_TRUSTED_TYPE: false
}

/**
 * 严格模式配置（仅允许纯文本）
 */
const STRICT_CONFIG = {
  ALLOWED_TAGS: [],
  ALLOWED_ATTR: [],
  KEEP_CONTENT: false
}

/**
 * 富文本编辑器配置（允许更多标签和属性）
 */
const RICH_TEXT_CONFIG = {
  ALLOWED_TAGS: [
    'p', 'br', 'strong', 'em', 'u', 's', 'sub', 'sup',
    'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
    'ul', 'ol', 'li',
    'blockquote', 'pre', 'code',
    'table', 'thead', 'tbody', 'tr', 'th', 'td',
    'a', 'img', 'div', 'span',
    'font', 'b', 'i'
  ],
  
  ALLOWED_ATTR: [
    'href', 'title', 'alt', 'src', 'width', 'height',
    'class', 'id', 'style', 'target', 'rel',
    'color', 'size', 'face'
  ]
}

/**
 * 清理HTML内容，防止XSS攻击
 * 
 * @param {string} dirty 需要清理的HTML字符串
 * @param {Object} config DOMPurify配置选项
 * @returns {string} 清理后的安全HTML字符串
 */
export function sanitizeHtml(dirty, config = DEFAULT_CONFIG) {
  if (typeof dirty !== 'string') {
    console.warn('XSS Protection: Input is not a string, returning empty string')
    return ''
  }
  
  if (!dirty.trim()) {
    return ''
  }
  
  try {
    return DOMPurify.sanitize(dirty, config)
  } catch (error) {
    console.error('XSS Protection: Error sanitizing HTML:', error)
    return ''
  }
}

/**
 * 严格模式清理（仅保留纯文本）
 * 
 * @param {string} dirty 需要清理的内容
 * @returns {string} 纯文本内容
 */
export function sanitizeText(dirty) {
  return sanitizeHtml(dirty, STRICT_CONFIG)
}

/**
 * 富文本内容清理（适用于富文本编辑器）
 * 
 * @param {string} dirty 需要清理的富文本内容
 * @returns {string} 清理后的富文本内容
 */
export function sanitizeRichText(dirty) {
  return sanitizeHtml(dirty, RICH_TEXT_CONFIG)
}

/**
 * 清理用户输入的表单数据
 * 
 * @param {Object} formData 表单数据对象
 * @param {Array} textFields 需要进行文本清理的字段名数组
 * @param {Array} htmlFields 需要进行HTML清理的字段名数组
 * @returns {Object} 清理后的表单数据
 */
export function sanitizeFormData(formData, textFields = [], htmlFields = []) {
  if (!formData || typeof formData !== 'object') {
    return formData
  }
  
  const cleanData = { ...formData }
  
  // 清理文本字段
  textFields.forEach(field => {
    if (cleanData[field] && typeof cleanData[field] === 'string') {
      cleanData[field] = sanitizeText(cleanData[field])
    }
  })
  
  // 清理HTML字段
  htmlFields.forEach(field => {
    if (cleanData[field] && typeof cleanData[field] === 'string') {
      cleanData[field] = sanitizeHtml(cleanData[field])
    }
  })
  
  return cleanData
}

/**
 * 清理URL，防止JavaScript伪协议
 * 
 * @param {string} url 需要清理的URL
 * @returns {string} 安全的URL
 */
export function sanitizeUrl(url) {
  if (typeof url !== 'string') {
    return ''
  }
  
  // 移除危险的协议
  const dangerousProtocols = ['javascript:', 'data:', 'vbscript:', 'file:', 'about:']
  const lowerUrl = url.toLowerCase().trim()
  
  for (const protocol of dangerousProtocols) {
    if (lowerUrl.startsWith(protocol)) {
      console.warn('XSS Protection: Dangerous protocol detected in URL:', url)
      return ''
    }
  }
  
  return url
}

/**
 * 转义HTML特殊字符
 * 
 * @param {string} text 需要转义的文本
 * @returns {string} 转义后的文本
 */
export function escapeHtml(text) {
  if (typeof text !== 'string') {
    return ''
  }
  
  const htmlEscapes = {
    '&': '&amp;',
    '<': '&lt;',
    '>': '&gt;',
    '"': '&quot;',
    "'": '&#x27;',
    '/': '&#x2F;'
  }
  
  return text.replace(/[&<>"'/]/g, (match) => htmlEscapes[match])
}

/**
 * Vue指令：自动清理HTML内容
 * 使用方法：v-xss-clean="htmlContent"
 */
export const xssCleanDirective = {
  bind(el, binding) {
    if (binding.value && typeof binding.value === 'string') {
      el.innerHTML = sanitizeHtml(binding.value)
    }
  },
  update(el, binding) {
    if (binding.value && typeof binding.value === 'string') {
      el.innerHTML = sanitizeHtml(binding.value)
    }
  }
}

/**
 * Vue过滤器：清理HTML内容
 * 使用方法：{{ htmlContent | xssClean }}
 */
export const xssCleanFilter = function(value, mode = 'default') {
  if (!value) return ''
  
  switch (mode) {
    case 'text':
      return sanitizeText(value)
    case 'rich':
      return sanitizeRichText(value)
    default:
      return sanitizeHtml(value)
  }
}

/**
 * 检查字符串是否包含潜在的XSS攻击代码
 * 
 * @param {string} input 需要检查的字符串
 * @returns {boolean} true表示可能包含XSS攻击代码
 */
export function detectXss(input) {
  if (typeof input !== 'string') {
    return false
  }
  
  const xssPatterns = [
    /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
    /javascript:/gi,
    /on\w+\s*=/gi,
    /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi,
    /<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/gi,
    /<embed\b[^<]*(?:(?!<\/embed>)<[^<]*)*<\/embed>/gi,
    /expression\s*\(/gi,
    /vbscript:/gi,
    /data:text\/html/gi
  ]
  
  return xssPatterns.some(pattern => pattern.test(input))
}

// 默认导出
export default {
  sanitizeHtml,
  sanitizeText,
  sanitizeRichText,
  sanitizeFormData,
  sanitizeUrl,
  escapeHtml,
  detectXss,
  xssCleanDirective,
  xssCleanFilter
}
