{"name": "vue-antd-jeecg", "version": "2.2.0", "private": true, "scripts": {"pre": "yarn --registry https://registry.npm.taobao.org || cnpm install || npm install --registry https://registry.npm.taobao.org ", "serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@antv/data-set": "^0.11.2", "@fullcalendar/core": "^6.1.17", "@fullcalendar/daygrid": "^6.1.17", "@fullcalendar/interaction": "^6.1.17", "@fullcalendar/timegrid": "^6.1.17", "@fullcalendar/vue": "^6.1.17", "@jeecg/antd-online-beta220": "^1.0.1", "@tinymce/tinymce-vue": "^2.1.0", "ant-design-vue": "^1.6.3", "area-data": "^5.0.6", "axios": "^1.6.7", "babel-core": "^6.26.3", "babel-loader": "7", "clipboard": "^2.0.4", "codemirror": "^5.46.0", "dayjs": "^1.8.0", "dompurify": "^3.2.6", "echarts": "^5.4.3", "enquire.js": "^2.1.6", "js-cookie": "^2.2.0", "lodash.get": "^4.4.2", "lodash.pick": "^4.4.0", "marked": "^4.3.0", "md5": "^2.2.1", "nprogress": "^0.2.0", "prismjs": "^1.26.0", "qrcode.vue": "^1.6.2", "swiper": "^4.1.5", "tinymce": "^5.3.2", "vcolorpicker": "^1.1.0", "viser-vue": "^2.4.4", "vue": "^2.6.10", "vue-area-linkage": "^5.1.0", "vue-awesome-swiper": "^4.1.1", "vue-codemirror-lite": "^1.0.4", "vue-cropper": "^0.4.8", "vue-i18n": "^8.7.0", "vue-loader": "^15.7.0", "vue-ls": "^3.2.0", "vue-photo-preview": "^1.1.3", "vue-print-nb-jeecg": "^1.0.9", "vue-router": "^3.0.1", "vue-splitpane": "^1.0.4", "vuedraggable": "^2.20.0", "vuex": "^3.1.0"}, "devDependencies": {"@babel/polyfill": "^7.2.5", "@vue/cli-plugin-babel": "^3.3.0", "@vue/cli-plugin-eslint": "^3.3.0", "@vue/cli-service": "^3.3.0", "@vue/eslint-config-standard": "^4.0.0", "babel-eslint": "7.2.3", "babel-plugin-import": "^1.13.0", "compression-webpack-plugin": "^3.1.0", "eslint": "^5.16.0", "eslint-plugin-vue": "^5.1.0", "html-webpack-plugin": "^4.2.0", "less": "^3.9.0", "less-loader": "^4.1.0"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/strongly-recommended", "@vue/standard"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {"generator-star-spacing": "off", "no-mixed-operators": 0, "vue/max-attributes-per-line": [2, {"singleline": 5, "multiline": {"max": 1, "allowFirstLine": false}}], "vue/attribute-hyphenation": 0, "vue/html-self-closing": 0, "vue/component-name-in-template-casing": 0, "vue/html-closing-bracket-spacing": 0, "vue/singleline-html-element-content-newline": 0, "vue/no-unused-components": 0, "vue/multiline-html-element-content-newline": 0, "vue/no-use-v-if-with-v-for": 0, "vue/html-closing-bracket-newline": 0, "vue/no-parsing-error": 0, "no-console": 0, "no-tabs": 0, "indent": [1, 4]}}, "postcss": {"plugins": {"autoprefixer": {}}}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"]}