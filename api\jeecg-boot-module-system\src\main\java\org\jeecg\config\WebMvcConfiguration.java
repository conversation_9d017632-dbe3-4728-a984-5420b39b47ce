package org.jeecg.config;

import org.jeecg.modules.shiro.authc.interceptor.OnlineInterceptor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.ViewControllerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Spring Boot 2.0 解决跨域问题
 *
 * <AUTHOR>
 *
 */
@Configuration
public class WebMvcConfiguration implements WebMvcConfigurer {

	@Value("${jeecg.path.upload}")
	private String upLoadPath;
	@Value("${jeecg.path.webapp}")
	private String webAppPath;
	@Value("${spring.resource.static-locations}")
	private String staticLocations;

	@Bean
	public OnlineInterceptor onlineInterceptor(){
		return new OnlineInterceptor();
	}

	@Bean
	public CorsFilter corsFilter() {
		final UrlBasedCorsConfigurationSource urlBasedCorsConfigurationSource = new UrlBasedCorsConfigurationSource();
		final CorsConfiguration corsConfiguration = new CorsConfiguration();

		/* 是否允许请求带有验证信息 */
		corsConfiguration.setAllowCredentials(true);

		/* 收紧CORS策略：不再允许所有来源，只允许特定域名 */
		// 开发环境允许localhost（Spring Boot 2.1.x兼容写法）
		corsConfiguration.addAllowedOrigin("http://localhost:80");
		corsConfiguration.addAllowedOrigin("http://localhost:8080");
		corsConfiguration.addAllowedOrigin("http://localhost:3000");
		corsConfiguration.addAllowedOrigin("http://127.0.0.1:80");
		corsConfiguration.addAllowedOrigin("http://127.0.0.1:8080");
		corsConfiguration.addAllowedOrigin("http://127.0.0.1:3000");
		// 生产环境需要配置具体域名
		// corsConfiguration.addAllowedOrigin("https://yourdomain.com");

		/* 允许服务端访问的客户端请求头 */
		corsConfiguration.addAllowedHeader("*");

		/* 允许访问的方法名,GET POST等 */
		corsConfiguration.addAllowedMethod("*");

		/* 添加安全头 */
		corsConfiguration.addExposedHeader("X-Total-Count");
		corsConfiguration.addExposedHeader("X-Access-Token");

		urlBasedCorsConfigurationSource.registerCorsConfiguration("/**", corsConfiguration);
		return new CorsFilter(urlBasedCorsConfigurationSource);
	}

	/**
	 * 静态资源的配置 - 使得可以从磁盘中读取 Html、图片、视频、音频等
	 */
	@Override
	public void addResourceHandlers(ResourceHandlerRegistry registry) {
		registry.addResourceHandler("/**")
		.addResourceLocations("file:" + upLoadPath + "//", "file:" + webAppPath + "//")
		.addResourceLocations(staticLocations.split(","));
	}

	/**
	 * 方案一： 默认访问根路径跳转 doc.html页面 （swagger文档页面）
	 * 方案二： 访问根路径改成跳转 index.html页面 （简化部署方案： 可以把前端打包直接放到项目的 webapp，上面的配置）
	 */
	@Override
	public void addViewControllers(ViewControllerRegistry registry) {
		registry.addViewController("/").setViewName("doc.html");
	}

	@Override
	public void addInterceptors(InterceptorRegistry registry) {
		String [] exculudes = new String[]{"/*.html","/html/**","/js/**","/css/**","/images/**"};
		registry.addInterceptor(onlineInterceptor()).excludePathPatterns(exculudes).addPathPatterns("/online/cgform/api/**");
	}
}
