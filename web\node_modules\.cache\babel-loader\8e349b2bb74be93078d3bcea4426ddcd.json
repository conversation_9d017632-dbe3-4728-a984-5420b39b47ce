{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\teachingproject\\teaching\\web\\src\\utils\\request.js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\utils\\request.js", "mtime": 1753797972856}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1745675041593}], "contextDependencies": [], "result": ["function _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport Vue from 'vue';\nimport axios from 'axios';\nimport store from '@/store';\nimport { VueAxios } from './axios';\nimport { Modal, notification } from 'ant-design-vue';\nimport { ACCESS_TOKEN } from \"@/store/mutation-types\";\n\n/**\n * 【指定 axios的 baseURL】\n * 如果手工指定 baseURL: '/jeecg-boot'\n * 则映射后端域名，通过 vue.config.js\n * @type {*|string}\n */\nvar apiBaseUrl = window._CONFIG['domianURL'] || \"/api\";\nconsole.log(\"apiBaseUrl= \", apiBaseUrl);\n// 创建 axios 实例\nvar service = axios.create({\n  baseURL: apiBaseUrl,\n  // api base_url\n  timeout: 60000 // 请求超时时间\n});\nvar err = function err(error) {\n  if (error.response) {\n    var data = error.response.data;\n    var token = Vue.ls.get(ACCESS_TOKEN);\n    console.log(\"------异常响应------\", token);\n    console.log(\"------异常响应------\", error.response.status);\n    switch (error.response.status) {\n      case 403:\n        notification.error({\n          message: '系统提示',\n          description: '拒绝访问',\n          duration: 4\n        });\n        break;\n      case 500:\n        //notification.error({ message: '系统提示', description:'Token失效，请重新登录!',duration: 4})\n        if (token && data.message == \"Token失效，请重新登录\") {\n          // update-begin- --- author:scott ------ date:20190225 ---- for:Token失效采用弹框模式，不直接跳转----\n          // store.dispatch('Logout').then(() => {\n          //     window.location.reload()\n          // })\n          Modal.error({\n            title: '登录已过期',\n            content: '很抱歉，登录已过期，请重新登录',\n            okText: '重新登录',\n            mask: false,\n            onOk: function onOk() {\n              store.dispatch('Logout').then(function () {\n                Vue.ls.remove(ACCESS_TOKEN);\n                window.location.reload();\n              });\n            }\n          });\n          // update-end- --- author:scott ------ date:20190225 ---- for:Token失效采用弹框模式，不直接跳转----\n        } else {\n          // 如果用户未登录，不显示错误提示，避免游客状态下的错误弹窗\n          if (token) {\n            notification.error({\n              message: '系统提示',\n              description: '服务器无法完成请求!',\n              duration: 4\n            });\n          } else {\n            console.log(\"游客状态下的500错误，已忽略提示:\", error.response.config.url);\n          }\n        }\n        break;\n      case 404:\n        // 如果用户未登录，不显示错误提示，避免游客状态下的错误弹窗\n        if (token) {\n          notification.error({\n            message: '系统提示',\n            description: '很抱歉，资源未找到!',\n            duration: 4\n          });\n        } else {\n          console.log(\"游客状态下的404错误，已忽略提示:\", error.response.config.url);\n        }\n        break;\n      case 504:\n        notification.error({\n          message: '系统提示',\n          description: '网络超时'\n        });\n        break;\n      case 401:\n        // 如果用户已登录但收到401错误，说明token失效，需要重新登录\n        if (token) {\n          notification.error({\n            message: '系统提示',\n            description: '未授权，请重新登录',\n            duration: 4\n          });\n          store.dispatch('Logout').then(function () {\n            setTimeout(function () {\n              window.location.reload();\n            }, 1500);\n          });\n        } else {\n          // 游客状态下的401错误，不显示提示\n          console.log(\"游客状态下的401错误，已忽略提示:\", error.response.config.url);\n        }\n        break;\n      default:\n        notification.error({\n          message: '系统提示',\n          description: data.message,\n          duration: 4\n        });\n        break;\n    }\n  }\n  return Promise.reject(error);\n};\n\n// request interceptor\nservice.interceptors.request.use(function (config) {\n  var token = Vue.ls.get(ACCESS_TOKEN);\n  if (token) {\n    config.headers['X-Access-Token'] = token; // 让每个请求携带自定义 token 请根据实际情况自行修改\n  }\n  if (config.method == 'get') {\n    if (config.url.indexOf(\"sys/dict/getDictItems\") < 0) {\n      config.params = _objectSpread({\n        _t: Date.parse(new Date()) / 1000\n      }, config.params);\n    }\n  }\n  return config;\n}, function (error) {\n  return Promise.reject(error);\n});\n\n// response interceptor\nservice.interceptors.response.use(function (response) {\n  return response.data;\n}, err);\nvar installer = {\n  vm: {},\n  install: function install(Vue) {\n    var router = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    Vue.use(VueAxios, router, service);\n  }\n};\nexport { installer as VueAxios, service as axios };", {"version": 3, "names": ["<PERSON><PERSON>", "axios", "store", "VueAxios", "Modal", "notification", "ACCESS_TOKEN", "apiBaseUrl", "window", "_CONFIG", "console", "log", "service", "create", "baseURL", "timeout", "err", "error", "response", "data", "token", "ls", "get", "status", "message", "description", "duration", "title", "content", "okText", "mask", "onOk", "dispatch", "then", "remove", "location", "reload", "config", "url", "setTimeout", "Promise", "reject", "interceptors", "request", "use", "headers", "method", "indexOf", "params", "_objectSpread", "_t", "Date", "parse", "installer", "vm", "install", "router", "arguments", "length", "undefined"], "sources": ["E:/teachingproject/teaching/web/src/utils/request.js"], "sourcesContent": ["import Vue from 'vue'\nimport axios from 'axios'\nimport store from '@/store'\nimport { VueAxios } from './axios'\nimport {Modal, notification} from 'ant-design-vue'\nimport { ACCESS_TOKEN } from \"@/store/mutation-types\"\n\n/**\n * 【指定 axios的 baseURL】\n * 如果手工指定 baseURL: '/jeecg-boot'\n * 则映射后端域名，通过 vue.config.js\n * @type {*|string}\n */\nlet apiBaseUrl = window._CONFIG['domianURL'] || \"/api\";\nconsole.log(\"apiBaseUrl= \",apiBaseUrl)\n// 创建 axios 实例\nconst service = axios.create({\n  baseURL: apiBaseUrl, // api base_url\n  timeout: 60000 // 请求超时时间\n})\n\nconst err = (error) => {\n  if (error.response) {\n    let data = error.response.data\n    const token = Vue.ls.get(ACCESS_TOKEN)\n    console.log(\"------异常响应------\",token)\n    console.log(\"------异常响应------\",error.response.status)\n    switch (error.response.status) {\n      case 403:\n        notification.error({ message: '系统提示', description: '拒绝访问',duration: 4})\n        break\n      case 500:\n        //notification.error({ message: '系统提示', description:'Token失效，请重新登录!',duration: 4})\n        if(token && data.message==\"Token失效，请重新登录\"){\n          // update-begin- --- author:scott ------ date:20190225 ---- for:Token失效采用弹框模式，不直接跳转----\n          // store.dispatch('Logout').then(() => {\n          //     window.location.reload()\n          // })\n          Modal.error({\n            title: '登录已过期',\n            content: '很抱歉，登录已过期，请重新登录',\n            okText: '重新登录',\n            mask: false,\n            onOk: () => {\n              store.dispatch('Logout').then(() => {\n                Vue.ls.remove(ACCESS_TOKEN)\n                window.location.reload()\n              })\n            }\n          })\n          // update-end- --- author:scott ------ date:20190225 ---- for:Token失效采用弹框模式，不直接跳转----\n        }else{\n          // 如果用户未登录，不显示错误提示，避免游客状态下的错误弹窗\n          if(token) {\n            notification.error({ message: '系统提示', description:'服务器无法完成请求!',duration: 4})\n          } else {\n            console.log(\"游客状态下的500错误，已忽略提示:\", error.response.config.url)\n          }\n        }\n        break\n      case 404:\n        // 如果用户未登录，不显示错误提示，避免游客状态下的错误弹窗\n        if(token) {\n          notification.error({ message: '系统提示', description:'很抱歉，资源未找到!',duration: 4})\n        } else {\n          console.log(\"游客状态下的404错误，已忽略提示:\", error.response.config.url)\n        }\n        break\n      case 504:\n        notification.error({ message: '系统提示', description: '网络超时'})\n        break\n      case 401:\n        // 如果用户已登录但收到401错误，说明token失效，需要重新登录\n        if (token) {\n          notification.error({ message: '系统提示', description:'未授权，请重新登录',duration: 4})\n          store.dispatch('Logout').then(() => {\n            setTimeout(() => {\n              window.location.reload()\n            }, 1500)\n          })\n        } else {\n          // 游客状态下的401错误，不显示提示\n          console.log(\"游客状态下的401错误，已忽略提示:\", error.response.config.url)\n        }\n        break\n      default:\n        notification.error({\n          message: '系统提示',\n          description: data.message,\n          duration: 4\n        })\n        break\n    }\n  }\n  return Promise.reject(error)\n};\n\n// request interceptor\nservice.interceptors.request.use(config => {\n  const token = Vue.ls.get(ACCESS_TOKEN)\n  if (token) {\n    config.headers[ 'X-Access-Token' ] = token // 让每个请求携带自定义 token 请根据实际情况自行修改\n  }\n  if(config.method=='get'){\n    if(config.url.indexOf(\"sys/dict/getDictItems\")<0){\n      config.params = {\n        _t: Date.parse(new Date())/1000,\n        ...config.params\n      }\n    }\n  }\n  return config\n},(error) => {\n  return Promise.reject(error)\n})\n\n// response interceptor\nservice.interceptors.response.use((response) => {\n    return response.data\n  }, err)\n\nconst installer = {\n  vm: {},\n  install (Vue, router = {}) {\n    Vue.use(VueAxios, router, service)\n  }\n}\n\nexport {\n  installer as VueAxios,\n  service as axios\n}"], "mappings": ";;;;;;AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,KAAK,MAAM,SAAS;AAC3B,SAASC,QAAQ,QAAQ,SAAS;AAClC,SAAQC,KAAK,EAAEC,YAAY,QAAO,gBAAgB;AAClD,SAASC,YAAY,QAAQ,wBAAwB;;AAErD;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,UAAU,GAAGC,MAAM,CAACC,OAAO,CAAC,WAAW,CAAC,IAAI,MAAM;AACtDC,OAAO,CAACC,GAAG,CAAC,cAAc,EAACJ,UAAU,CAAC;AACtC;AACA,IAAMK,OAAO,GAAGX,KAAK,CAACY,MAAM,CAAC;EAC3BC,OAAO,EAAEP,UAAU;EAAE;EACrBQ,OAAO,EAAE,KAAK,CAAC;AACjB,CAAC,CAAC;AAEF,IAAMC,GAAG,GAAG,SAANA,GAAGA,CAAIC,KAAK,EAAK;EACrB,IAAIA,KAAK,CAACC,QAAQ,EAAE;IAClB,IAAIC,IAAI,GAAGF,KAAK,CAACC,QAAQ,CAACC,IAAI;IAC9B,IAAMC,KAAK,GAAGpB,GAAG,CAACqB,EAAE,CAACC,GAAG,CAAChB,YAAY,CAAC;IACtCI,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAACS,KAAK,CAAC;IACrCV,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAACM,KAAK,CAACC,QAAQ,CAACK,MAAM,CAAC;IACrD,QAAQN,KAAK,CAACC,QAAQ,CAACK,MAAM;MAC3B,KAAK,GAAG;QACNlB,YAAY,CAACY,KAAK,CAAC;UAAEO,OAAO,EAAE,MAAM;UAAEC,WAAW,EAAE,MAAM;UAACC,QAAQ,EAAE;QAAC,CAAC,CAAC;QACvE;MACF,KAAK,GAAG;QACN;QACA,IAAGN,KAAK,IAAID,IAAI,CAACK,OAAO,IAAE,eAAe,EAAC;UACxC;UACA;UACA;UACA;UACApB,KAAK,CAACa,KAAK,CAAC;YACVU,KAAK,EAAE,OAAO;YACdC,OAAO,EAAE,iBAAiB;YAC1BC,MAAM,EAAE,MAAM;YACdC,IAAI,EAAE,KAAK;YACXC,IAAI,EAAE,SAAAA,KAAA,EAAM;cACV7B,KAAK,CAAC8B,QAAQ,CAAC,QAAQ,CAAC,CAACC,IAAI,CAAC,YAAM;gBAClCjC,GAAG,CAACqB,EAAE,CAACa,MAAM,CAAC5B,YAAY,CAAC;gBAC3BE,MAAM,CAAC2B,QAAQ,CAACC,MAAM,CAAC,CAAC;cAC1B,CAAC,CAAC;YACJ;UACF,CAAC,CAAC;UACF;QACF,CAAC,MAAI;UACH;UACA,IAAGhB,KAAK,EAAE;YACRf,YAAY,CAACY,KAAK,CAAC;cAAEO,OAAO,EAAE,MAAM;cAAEC,WAAW,EAAC,YAAY;cAACC,QAAQ,EAAE;YAAC,CAAC,CAAC;UAC9E,CAAC,MAAM;YACLhB,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEM,KAAK,CAACC,QAAQ,CAACmB,MAAM,CAACC,GAAG,CAAC;UAC9D;QACF;QACA;MACF,KAAK,GAAG;QACN;QACA,IAAGlB,KAAK,EAAE;UACRf,YAAY,CAACY,KAAK,CAAC;YAAEO,OAAO,EAAE,MAAM;YAAEC,WAAW,EAAC,YAAY;YAACC,QAAQ,EAAE;UAAC,CAAC,CAAC;QAC9E,CAAC,MAAM;UACLhB,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEM,KAAK,CAACC,QAAQ,CAACmB,MAAM,CAACC,GAAG,CAAC;QAC9D;QACA;MACF,KAAK,GAAG;QACNjC,YAAY,CAACY,KAAK,CAAC;UAAEO,OAAO,EAAE,MAAM;UAAEC,WAAW,EAAE;QAAM,CAAC,CAAC;QAC3D;MACF,KAAK,GAAG;QACN;QACA,IAAIL,KAAK,EAAE;UACTf,YAAY,CAACY,KAAK,CAAC;YAAEO,OAAO,EAAE,MAAM;YAAEC,WAAW,EAAC,WAAW;YAACC,QAAQ,EAAE;UAAC,CAAC,CAAC;UAC3ExB,KAAK,CAAC8B,QAAQ,CAAC,QAAQ,CAAC,CAACC,IAAI,CAAC,YAAM;YAClCM,UAAU,CAAC,YAAM;cACf/B,MAAM,CAAC2B,QAAQ,CAACC,MAAM,CAAC,CAAC;YAC1B,CAAC,EAAE,IAAI,CAAC;UACV,CAAC,CAAC;QACJ,CAAC,MAAM;UACL;UACA1B,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEM,KAAK,CAACC,QAAQ,CAACmB,MAAM,CAACC,GAAG,CAAC;QAC9D;QACA;MACF;QACEjC,YAAY,CAACY,KAAK,CAAC;UACjBO,OAAO,EAAE,MAAM;UACfC,WAAW,EAAEN,IAAI,CAACK,OAAO;UACzBE,QAAQ,EAAE;QACZ,CAAC,CAAC;QACF;IACJ;EACF;EACA,OAAOc,OAAO,CAACC,MAAM,CAACxB,KAAK,CAAC;AAC9B,CAAC;;AAED;AACAL,OAAO,CAAC8B,YAAY,CAACC,OAAO,CAACC,GAAG,CAAC,UAAAP,MAAM,EAAI;EACzC,IAAMjB,KAAK,GAAGpB,GAAG,CAACqB,EAAE,CAACC,GAAG,CAAChB,YAAY,CAAC;EACtC,IAAIc,KAAK,EAAE;IACTiB,MAAM,CAACQ,OAAO,CAAE,gBAAgB,CAAE,GAAGzB,KAAK,EAAC;EAC7C;EACA,IAAGiB,MAAM,CAACS,MAAM,IAAE,KAAK,EAAC;IACtB,IAAGT,MAAM,CAACC,GAAG,CAACS,OAAO,CAAC,uBAAuB,CAAC,GAAC,CAAC,EAAC;MAC/CV,MAAM,CAACW,MAAM,GAAAC,aAAA;QACXC,EAAE,EAAEC,IAAI,CAACC,KAAK,CAAC,IAAID,IAAI,CAAC,CAAC,CAAC,GAAC;MAAI,GAC5Bd,MAAM,CAACW,MAAM,CACjB;IACH;EACF;EACA,OAAOX,MAAM;AACf,CAAC,EAAC,UAACpB,KAAK,EAAK;EACX,OAAOuB,OAAO,CAACC,MAAM,CAACxB,KAAK,CAAC;AAC9B,CAAC,CAAC;;AAEF;AACAL,OAAO,CAAC8B,YAAY,CAACxB,QAAQ,CAAC0B,GAAG,CAAC,UAAC1B,QAAQ,EAAK;EAC5C,OAAOA,QAAQ,CAACC,IAAI;AACtB,CAAC,EAAEH,GAAG,CAAC;AAET,IAAMqC,SAAS,GAAG;EAChBC,EAAE,EAAE,CAAC,CAAC;EACNC,OAAO,WAAAA,QAAEvD,GAAG,EAAe;IAAA,IAAbwD,MAAM,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IACvBzD,GAAG,CAAC4C,GAAG,CAACzC,QAAQ,EAAEqD,MAAM,EAAE5C,OAAO,CAAC;EACpC;AACF,CAAC;AAED,SACEyC,SAAS,IAAIlD,QAAQ,EACrBS,OAAO,IAAIX,KAAK", "ignoreList": []}]}