package org.jeecg.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.parser.ParserConfig;
import com.alibaba.fastjson.serializer.SerializeConfig;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.fastjson.serializer.SimplePropertyPreFilter;
import com.alibaba.fastjson.support.config.FastJsonConfig;
import com.alibaba.fastjson.support.spring.FastJsonHttpMessageConverter;
import org.apache.shiro.web.servlet.ShiroHttpServletRequest;

import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;

/**
 * FastJson配置类
 * 用于解决ShiroHttpServletRequest序列化问题
 */
@Configuration
public class FastJsonHttpMessageConverterConfig implements WebMvcConfigurer {

    /**
     * 初始化FastJSON安全配置
     * 缓解FastJSON反序列化漏洞
     */
    @PostConstruct
    public void initFastJsonSecurity() {
        // 关闭AutoType功能，防止反序列化漏洞
        ParserConfig.getGlobalInstance().setAutoTypeSupport(false);

        // 设置安全模式
        ParserConfig.getGlobalInstance().setSafeMode(true);

        // 禁用危险的AutoType类
        ParserConfig.getGlobalInstance().addDeny("com.sun.");
        ParserConfig.getGlobalInstance().addDeny("java.rmi.");
        ParserConfig.getGlobalInstance().addDeny("javax.script.");
        ParserConfig.getGlobalInstance().addDeny("com.mchange.");
        ParserConfig.getGlobalInstance().addDeny("org.apache.commons.collections.Transformer");
        ParserConfig.getGlobalInstance().addDeny("org.apache.commons.collections.functors");
    }

    @Override
    public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
        // 创建FastJson消息转换器
        FastJsonHttpMessageConverter fastConverter = new FastJsonHttpMessageConverter();
        
        // 创建配置类
        FastJsonConfig fastJsonConfig = new FastJsonConfig();
        
        // 修复ShiroHttpServletRequest序列化问题
        SerializeConfig serializeConfig = SerializeConfig.globalInstance;
        // 排除ShiroHttpServletRequest类，避免序列化错误
        serializeConfig.put(ShiroHttpServletRequest.class, (jsonSerializer, object, fieldName, fieldType, features) -> {
            // 不序列化ShiroHttpServletRequest对象，返回null
            jsonSerializer.write(null);
        });
        
        // 设置SerializeConfig
        fastJsonConfig.setSerializeConfig(serializeConfig);
        
        // 设置序列化特性
        fastJsonConfig.setSerializerFeatures(
            SerializerFeature.DisableCircularReferenceDetect,  // 禁用循环引用检测
            SerializerFeature.WriteMapNullValue,               // 输出空值字段
            SerializerFeature.WriteNullStringAsEmpty           // 将null值字符串输出为空字符串
        );
        
        // 添加自定义过滤器，排除可能导致问题的字段
        SimplePropertyPreFilter filter = new SimplePropertyPreFilter();
        filter.getExcludes().add("T");  // 排除名为"T"的字段
        fastJsonConfig.setSerializeFilters(filter);
        
        // 设置支持的MediaType
        List<MediaType> fastMediaTypes = new ArrayList<>();
        fastMediaTypes.add(MediaType.APPLICATION_JSON);
        fastConverter.setSupportedMediaTypes(fastMediaTypes);
        
        // 将配置应用到转换器
        fastConverter.setFastJsonConfig(fastJsonConfig);
        
        // 添加到转换器列表中，并设置为首位
        converters.add(0, fastConverter);
    }
} 