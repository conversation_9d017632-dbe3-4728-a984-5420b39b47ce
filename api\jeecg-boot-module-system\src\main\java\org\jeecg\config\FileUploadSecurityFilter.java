package org.jeecg.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;

/**
 * 文件上传安全过滤器
 * 用于检查上传文件的安全性，防止恶意文件上传
 * 
 * <AUTHOR> System
 * @date 2024-01-01
 */
@Component
@Slf4j
public class FileUploadSecurityFilter {

    /**
     * 允许的文件扩展名
     */
    private static final List<String> ALLOWED_EXTENSIONS = Arrays.asList(
        ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp",  // 图片文件
        ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx",  // 办公文档
        ".txt", ".md", ".csv",  // 文本文件
        ".zip", ".rar", ".7z",  // 压缩文件
        ".mp4", ".avi", ".mov", ".wmv",  // 视频文件
        ".mp3", ".wav", ".flac"  // 音频文件
    );

    /**
     * 允许的MIME类型
     */
    private static final List<String> ALLOWED_MIME_TYPES = Arrays.asList(
        "image/jpeg", "image/png", "image/gif", "image/bmp", "image/webp",
        "application/pdf",
        "application/msword", "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "application/vnd.ms-excel", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        "application/vnd.ms-powerpoint", "application/vnd.openxmlformats-officedocument.presentationml.presentation",
        "text/plain", "text/csv",
        "application/zip", "application/x-rar-compressed", "application/x-7z-compressed",
        "video/mp4", "video/avi", "video/quicktime", "video/x-ms-wmv",
        "audio/mpeg", "audio/wav", "audio/flac"
    );

    /**
     * 最大文件大小（10MB）
     */
    private static final long MAX_FILE_SIZE = 10 * 1024 * 1024;

    /**
     * 危险文件头魔数
     */
    private static final byte[][] DANGEROUS_FILE_HEADERS = {
        {0x4D, 0x5A},  // PE/EXE文件
        {0x50, 0x4B},  // ZIP文件（需要进一步检查）
        {0x7F, 0x45, 0x4C, 0x46},  // ELF文件
        {(byte)0xCA, (byte)0xFE, (byte)0xBA, (byte)0xBE},  // Java class文件
        {(byte)0xFE, (byte)0xED, (byte)0xFA, (byte)0xCE}   // Mach-O文件
    };

    /**
     * 检查文件是否安全
     * 
     * @param file 上传的文件
     * @return true表示文件安全，false表示文件不安全
     */
    public boolean isSecureFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            log.warn("文件为空或不存在");
            return false;
        }

        try {
            // 1. 检查文件扩展名
            if (!isAllowedExtension(file.getOriginalFilename())) {
                log.warn("不允许的文件扩展名: {}", file.getOriginalFilename());
                return false;
            }

            // 2. 检查文件大小
            if (file.getSize() > MAX_FILE_SIZE) {
                log.warn("文件大小超过限制: {} bytes, 最大允许: {} bytes", file.getSize(), MAX_FILE_SIZE);
                return false;
            }

            // 3. 检查文件内容类型
            if (!isAllowedContentType(file.getContentType())) {
                log.warn("不允许的文件内容类型: {}", file.getContentType());
                return false;
            }

            // 4. 检查文件头魔数
            if (!isValidFileHeader(file)) {
                log.warn("检测到危险的文件头: {}", file.getOriginalFilename());
                return false;
            }

            log.info("文件安全检查通过: {}", file.getOriginalFilename());
            return true;

        } catch (Exception e) {
            log.error("文件安全检查异常: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 检查文件扩展名是否允许
     */
    private boolean isAllowedExtension(String filename) {
        if (filename == null || filename.trim().isEmpty()) {
            return false;
        }

        String lowerFilename = filename.toLowerCase();
        return ALLOWED_EXTENSIONS.stream()
                .anyMatch(lowerFilename::endsWith);
    }

    /**
     * 检查文件内容类型是否允许
     */
    private boolean isAllowedContentType(String contentType) {
        if (contentType == null || contentType.trim().isEmpty()) {
            return false;
        }

        return ALLOWED_MIME_TYPES.contains(contentType.toLowerCase());
    }

    /**
     * 检查文件头是否有效（检测危险文件）
     */
    private boolean isValidFileHeader(MultipartFile file) {
        try {
            byte[] fileHeader = new byte[8];
            int bytesRead = file.getInputStream().read(fileHeader);
            
            if (bytesRead <= 0) {
                return false;
            }

            // 检查是否为危险文件头
            for (byte[] dangerousHeader : DANGEROUS_FILE_HEADERS) {
                if (bytesRead >= dangerousHeader.length && 
                    startsWith(fileHeader, dangerousHeader)) {
                    return false;
                }
            }

            return true;

        } catch (IOException e) {
            log.error("读取文件头失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 检查字节数组是否以指定的字节序列开头
     */
    private boolean startsWith(byte[] array, byte[] prefix) {
        if (array.length < prefix.length) {
            return false;
        }

        for (int i = 0; i < prefix.length; i++) {
            if (array[i] != prefix[i]) {
                return false;
            }
        }

        return true;
    }

    /**
     * 获取文件扩展名
     */
    public String getFileExtension(String filename) {
        if (filename == null || filename.trim().isEmpty()) {
            return "";
        }

        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex == -1 || lastDotIndex == filename.length() - 1) {
            return "";
        }

        return filename.substring(lastDotIndex).toLowerCase();
    }

    /**
     * 生成安全的文件名
     */
    public String generateSecureFilename(String originalFilename) {
        if (originalFilename == null || originalFilename.trim().isEmpty()) {
            return "unknown_" + System.currentTimeMillis();
        }

        // 移除危险字符
        String safeName = originalFilename.replaceAll("[^a-zA-Z0-9._-]", "_");
        
        // 限制文件名长度
        if (safeName.length() > 100) {
            String extension = getFileExtension(safeName);
            String nameWithoutExt = safeName.substring(0, safeName.lastIndexOf('.'));
            safeName = nameWithoutExt.substring(0, 100 - extension.length()) + extension;
        }

        return safeName;
    }
}
