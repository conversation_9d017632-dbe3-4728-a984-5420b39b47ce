package org.jeecg.config;

import org.springframework.boot.web.servlet.MultipartConfigFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.unit.DataSize;
import org.springframework.web.multipart.MultipartResolver;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;

import javax.servlet.MultipartConfigElement;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * 文件上传安全配置
 * 加强文件上传安全性，防止恶意文件上传
 */
@Configuration
public class FileUploadSecurityConfig {

    /**
     * 允许的文件扩展名
     */
    private static final Set<String> ALLOWED_EXTENSIONS = new HashSet<>(Arrays.asList(
        // 图片文件
        "jpg", "jpeg", "png", "gif", "bmp", "webp",
        // 文档文件
        "pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx", "txt",
        // 压缩文件
        "zip", "rar", "7z",
        // 音视频文件
        "mp3", "mp4", "avi", "mov", "wmv", "flv"
    ));

    /**
     * 禁止的文件扩展名（可执行文件等）
     */
    private static final Set<String> FORBIDDEN_EXTENSIONS = new HashSet<>(Arrays.asList(
        "exe", "bat", "cmd", "com", "pif", "scr", "vbs", "js", "jar", "jsp", "php", "asp", "aspx"
    ));

    /**
     * 文件上传配置
     */
    @Bean
    public MultipartConfigElement multipartConfigElement() {
        MultipartConfigFactory factory = new MultipartConfigFactory();
        
        // 设置文件大小限制（50MB）
        factory.setMaxFileSize(DataSize.ofMegabytes(50));
        
        // 设置总上传数据大小限制（100MB）
        factory.setMaxRequestSize(DataSize.ofMegabytes(100));
        
        return factory.createMultipartConfig();
    }

    /**
     * 多部分解析器配置
     */
    @Bean
    public MultipartResolver multipartResolver() {
        CommonsMultipartResolver resolver = new CommonsMultipartResolver();
        
        // 设置编码
        resolver.setDefaultEncoding("UTF-8");
        
        // 设置最大文件大小（50MB）
        resolver.setMaxUploadSize(50 * 1024 * 1024);
        
        // 设置单个文件最大大小（50MB）
        resolver.setMaxUploadSizePerFile(50 * 1024 * 1024);
        
        return resolver;
    }

    /**
     * 验证文件扩展名是否安全
     * @param fileName 文件名
     * @return 是否安全
     */
    public static boolean isFileExtensionSafe(String fileName) {
        if (fileName == null || fileName.trim().isEmpty()) {
            return false;
        }
        
        String extension = getFileExtension(fileName).toLowerCase();
        
        // 检查是否在禁止列表中
        if (FORBIDDEN_EXTENSIONS.contains(extension)) {
            return false;
        }
        
        // 检查是否在允许列表中
        return ALLOWED_EXTENSIONS.contains(extension);
    }

    /**
     * 获取文件扩展名
     * @param fileName 文件名
     * @return 扩展名
     */
    private static String getFileExtension(String fileName) {
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex == -1 || lastDotIndex == fileName.length() - 1) {
            return "";
        }
        return fileName.substring(lastDotIndex + 1);
    }

    /**
     * 验证文件MIME类型是否安全
     * @param contentType MIME类型
     * @return 是否安全
     */
    public static boolean isMimeTypeSafe(String contentType) {
        if (contentType == null || contentType.trim().isEmpty()) {
            return false;
        }
        
        // 允许的MIME类型
        Set<String> allowedMimeTypes = new HashSet<>(Arrays.asList(
            // 图片
            "image/jpeg", "image/png", "image/gif", "image/bmp", "image/webp",
            // 文档
            "application/pdf", 
            "application/msword", 
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "application/vnd.ms-excel",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "application/vnd.ms-powerpoint",
            "application/vnd.openxmlformats-officedocument.presentationml.presentation",
            "text/plain",
            // 压缩文件
            "application/zip", "application/x-rar-compressed", "application/x-7z-compressed",
            // 音视频
            "audio/mpeg", "video/mp4", "video/avi", "video/quicktime"
        ));
        
        return allowedMimeTypes.contains(contentType.toLowerCase());
    }

    /**
     * 验证文件名是否安全（防止路径遍历攻击）
     * @param fileName 文件名
     * @return 是否安全
     */
    public static boolean isFileNameSafe(String fileName) {
        if (fileName == null || fileName.trim().isEmpty()) {
            return false;
        }
        
        // 检查是否包含路径遍历字符
        if (fileName.contains("..") || fileName.contains("/") || fileName.contains("\\")) {
            return false;
        }
        
        // 检查是否包含特殊字符
        String[] forbiddenChars = {"<", ">", ":", "\"", "|", "?", "*"};
        for (String forbiddenChar : forbiddenChars) {
            if (fileName.contains(forbiddenChar)) {
                return false;
            }
        }
        
        return true;
    }
}
