import Vue from 'vue'
import axios from 'axios'
import store from '@/store'
import { VueAxios } from './axios'
import {Modal, notification} from 'ant-design-vue'
import { ACCESS_TOKEN } from "@/store/mutation-types"

/**
 * 【指定 axios的 baseURL】
 * 如果手工指定 baseURL: '/jeecg-boot'
 * 则映射后端域名，通过 vue.config.js
 * @type {*|string}
 */
let apiBaseUrl = window._CONFIG['domianURL'] || "/api";
console.log("apiBaseUrl= ",apiBaseUrl)
// 创建 axios 实例
const service = axios.create({
  baseURL: apiBaseUrl, // api base_url
  timeout: 60000, // 请求超时时间
  // 安全配置增强
  withCredentials: true, // 允许跨域请求携带凭证
  maxRedirects: 5, // 最大重定向次数
  maxContentLength: 50 * 1024 * 1024, // 最大响应内容长度 50MB
  validateStatus: function (status) {
    // 定义有效的HTTP状态码范围
    return status >= 200 && status < 300;
  }
})

const err = (error) => {
  if (error.response) {
    let data = error.response.data
    const token = Vue.ls.get(ACCESS_TOKEN)
    console.log("------异常响应------",token)
    console.log("------异常响应------",error.response.status)
    switch (error.response.status) {
      case 403:
        notification.error({ message: '系统提示', description: '拒绝访问',duration: 4})
        break
      case 500:
        //notification.error({ message: '系统提示', description:'Token失效，请重新登录!',duration: 4})
        if(token && data.message=="Token失效，请重新登录"){
          // update-begin- --- author:scott ------ date:20190225 ---- for:Token失效采用弹框模式，不直接跳转----
          // store.dispatch('Logout').then(() => {
          //     window.location.reload()
          // })
          Modal.error({
            title: '登录已过期',
            content: '很抱歉，登录已过期，请重新登录',
            okText: '重新登录',
            mask: false,
            onOk: () => {
              store.dispatch('Logout').then(() => {
                Vue.ls.remove(ACCESS_TOKEN)
                window.location.reload()
              })
            }
          })
          // update-end- --- author:scott ------ date:20190225 ---- for:Token失效采用弹框模式，不直接跳转----
        }else{
          // 如果用户未登录，不显示错误提示，避免游客状态下的错误弹窗
          if(token) {
            notification.error({ message: '系统提示', description:'服务器无法完成请求!',duration: 4})
          } else {
            console.log("游客状态下的500错误，已忽略提示:", error.response.config.url)
          }
        }
        break
      case 404:
        // 如果用户未登录，不显示错误提示，避免游客状态下的错误弹窗
        if(token) {
          notification.error({ message: '系统提示', description:'很抱歉，资源未找到!',duration: 4})
        } else {
          console.log("游客状态下的404错误，已忽略提示:", error.response.config.url)
        }
        break
      case 504:
        notification.error({ message: '系统提示', description: '网络超时'})
        break
      case 401:
        // 如果用户已登录但收到401错误，说明token失效，需要重新登录
        if (token) {
          notification.error({ message: '系统提示', description:'未授权，请重新登录',duration: 4})
          store.dispatch('Logout').then(() => {
            setTimeout(() => {
              window.location.reload()
            }, 1500)
          })
        } else {
          // 游客状态下的401错误，不显示提示
          console.log("游客状态下的401错误，已忽略提示:", error.response.config.url)
        }
        break
      default:
        notification.error({
          message: '系统提示',
          description: data.message,
          duration: 4
        })
        break
    }
  }
  return Promise.reject(error)
};

// request interceptor
service.interceptors.request.use(config => {
  const token = Vue.ls.get(ACCESS_TOKEN)
  if (token) {
    config.headers[ 'X-Access-Token' ] = token // 让每个请求携带自定义 token 请根据实际情况自行修改
  }
  if(config.method=='get'){
    if(config.url.indexOf("sys/dict/getDictItems")<0){
      config.params = {
        _t: Date.parse(new Date())/1000,
        ...config.params
      }
    }
  }
  return config
},(error) => {
  return Promise.reject(error)
})

// response interceptor
service.interceptors.response.use((response) => {
    return response.data
  }, err)

const installer = {
  vm: {},
  install (Vue, router = {}) {
    Vue.use(VueAxios, router, service)
  }
}

export {
  installer as VueAxios,
  service as axios
}